#!/usr/bin/env python3
"""
Example script to demonstrate the log_processing_time decorator functionality.
This shows how the decorator automatically handles logging and timing.
"""

import asyncio
import time
from app.core.decorators import log_processing_time, simple_timer
from app.models.predict_request import PredictRequest, ArtDetails
from app.core.logging_config import setup_logging


# Example 1: Using the full decorator with PredictRequest
@log_processing_time("Test Artwork Analysis")
async def test_artwork_analysis(request: PredictRequest) -> dict:
    """Test function that simulates artwork analysis."""
    # Simulate some processing time
    await asyncio.sleep(1.5)
    
    return {
        "status": "success",
        "analysis": "Test analysis complete",
        "confidence": 0.95
    }


# Example 2: Using the simple timer decorator
@simple_timer("Database Operation")
async def test_database_operation():
    """Test function that simulates a database operation."""
    await asyncio.sleep(0.8)
    return {"data": "retrieved"}


# Example 3: Function that raises an exception to test error logging
@log_processing_time("Error Test Operation")
async def test_error_operation(request: PredictRequest) -> dict:
    """Test function that raises an exception."""
    await asyncio.sleep(0.5)
    raise ValueError("This is a test error to demonstrate error logging")


async def main():
    """Run the decorator examples."""
    # Setup logging
    setup_logging()
    
    print("🧪 Testing decorators...\n")
    
    # Create a test request
    art_details = ArtDetails(
        user_id="test_user_123",
        title="The Starry Night",
        artist="Vincent van Gogh",
        year=1889,
        medium="Oil on canvas",
        dimensions="73.7 cm × 92.1 cm"
    )
    
    test_request = PredictRequest(
        art_details=art_details,
        analysis_type="condition_assessment",
        priority="high"
    )
    
    print("=" * 60)
    print("1. Testing successful artwork analysis with full logging:")
    print("=" * 60)
    try:
        result = await test_artwork_analysis(test_request)
        print(f"Result: {result}\n")
    except Exception as e:
        print(f"Error: {e}\n")
    
    print("=" * 60)
    print("2. Testing simple timer decorator:")
    print("=" * 60)
    try:
        result = await test_database_operation()
        print(f"Result: {result}\n")
    except Exception as e:
        print(f"Error: {e}\n")
    
    print("=" * 60)
    print("3. Testing error handling with decorator:")
    print("=" * 60)
    try:
        result = await test_error_operation(test_request)
        print(f"Result: {result}\n")
    except Exception as e:
        print(f"Caught exception: {e}\n")
    
    print("🎉 Decorator testing complete!")


if __name__ == "__main__":
    asyncio.run(main())
