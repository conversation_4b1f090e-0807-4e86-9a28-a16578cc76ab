from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import time

from app.core.decorators import log_processing_time
from app.models.predict_request import PredictRequest
from app.services.predict import PredictService

router = APIRouter()

@router.post("/predict")
@log_processing_time("Artwork Analysis")
async def get_therapist_response(r: PredictRequest) -> Dict[str, Any]:
    """
    Analyze artwork condition based on provided parameters and image.

    Args:
        r: PredictRequest containing artwork details and analysis parameters

    Returns:
        Dict containing analysis status and details
    """
    service = PredictService()

    try:
        # Execute the prediction service
        service.predict(r)

        # Create response with request ID
        # Note: The decorator handles request_id generation and logging automatically
        request_id = f"{r.art_details.user_id}_{int(time.time())}"
        
        response_data = {
            "status": "success",
            "message": "Analysis started.",
            "request_id": request_id,
            "details": r.model_dump()
        }

        return response_data

    except Exception as e:
        # Re-raise as HTTP exception - decorator handles error logging
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error processing analysis request: {str(e)}"
        )
