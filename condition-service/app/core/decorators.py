import time
import functools
from typing import Any, Callable, Dict, Optional
import logging
from app.core.logging_config import get_logger
from app.models.predict_request import PredictRequest


def log_processing_time(
    operation_name: str = "Operation",
    include_request_details: bool = True,
    logger_name: Optional[str] = None
):
    """
    Decorator to automatically log processing time and request details for async functions.
    
    Args:
        operation_name: Name of the operation for logging (e.g., "Artwork Analysis")
        include_request_details: Whether to log request details for PredictRequest
        logger_name: Custom logger name, defaults to the decorated function's module
    
    Usage:
        @log_processing_time("Artwork Analysis")
        async def get_therapist_response(r: PredictRequest) -> Dict[str, Any]:
            # Your function logic here
            return result
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            # Get logger
            logger = get_logger(logger_name or func.__module__)
            
            # Start timing
            start_time = time.time()
            
            # Generate request ID and extract request details
            request_id = None
            request_details = {}
            
            # Look for PredictRequest in args or kwargs
            predict_request = None
            for arg in args:
                if isinstance(arg, PredictRequest):
                    predict_request = arg
                    break
            
            if not predict_request:
                for value in kwargs.values():
                    if isinstance(value, PredictRequest):
                        predict_request = value
                        break
            
            if predict_request:
                request_id = f"{predict_request.art_details.user_id}_{int(start_time)}"
                if include_request_details:
                    request_details = {
                        "user_id": predict_request.art_details.user_id,
                        "title": predict_request.art_details.title,
                        "artist": predict_request.art_details.artist
                    }
            else:
                request_id = f"req_{int(start_time)}"
            
            # Log operation start
            logger.info(f"🎨 Starting {operation_name} - Request ID: {request_id}")
            if request_details:
                logger.info(f"User: {request_details['user_id']}")
                logger.info(f"Artwork: '{request_details['title']}' by {request_details['artist']}")
            
            logger.info(f"🔍 Processing {operation_name.lower()} for request {request_id}...")
            
            try:
                # Execute the original function
                result = await func(*args, **kwargs)
                
                # Log successful completion
                processing_time = time.time() - start_time
                logger.info(f"✅ {operation_name} completed successfully - Request ID: {request_id}")
                logger.info(f"Processing time: {processing_time:.3f} seconds")
                
                return result
                
            except Exception as e:
                # Log error details
                processing_time = time.time() - start_time
                logger.error(f"❌ Error in {operation_name} - Request ID: {request_id}: {str(e)}")
                logger.error(f"Processing time before error: {processing_time:.3f} seconds")
                logger.exception("Full error traceback:")
                
                # Re-raise the exception
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            # Similar implementation for sync functions
            logger = get_logger(logger_name or func.__module__)
            start_time = time.time()
            
            # Generate request ID (simplified for sync functions)
            request_id = f"sync_req_{int(start_time)}"
            
            logger.info(f"🎨 Starting {operation_name} - Request ID: {request_id}")
            logger.info(f"🔍 Processing {operation_name.lower()} for request {request_id}...")
            
            try:
                result = func(*args, **kwargs)
                
                processing_time = time.time() - start_time
                logger.info(f"✅ {operation_name} completed successfully - Request ID: {request_id}")
                logger.info(f"Processing time: {processing_time:.3f} seconds")
                
                return result
                
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"❌ Error in {operation_name} - Request ID: {request_id}: {str(e)}")
                logger.error(f"Processing time before error: {processing_time:.3f} seconds")
                logger.exception("Full error traceback:")
                raise
        
        # Return appropriate wrapper based on whether function is async
        if functools.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def simple_timer(operation_name: str = "Operation"):
    """
    Simple decorator that only logs processing time without request details.
    Useful for utility functions or when you don't need detailed logging.
    
    Usage:
        @simple_timer("Database Query")
        async def fetch_data():
            # Your function logic here
            return data
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            logger = get_logger(func.__module__)
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                processing_time = time.time() - start_time
                logger.info(f"{operation_name} completed in {processing_time:.3f} seconds")
                return result
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"{operation_name} failed after {processing_time:.3f} seconds: {str(e)}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            logger = get_logger(func.__module__)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                processing_time = time.time() - start_time
                logger.info(f"{operation_name} completed in {processing_time:.3f} seconds")
                return result
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"{operation_name} failed after {processing_time:.3f} seconds: {str(e)}")
                raise
        
        if functools.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
