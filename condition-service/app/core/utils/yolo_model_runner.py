import os
from typing import Optional, List

from pydantic import BaseModel

from app import logger
from app.core.config import settings
from app.core.utils.s3 import get_model
from ultralytics import YOLO


class YoloModelRunner(BaseModel):
    model: <PERSON><PERSON><PERSON>

    def __init__(self, model_bucket: str, model_s3_key: str) -> None:
        local_model_path = get_model(model_bucket, model_s3_key)
        self.model = YOLO(local_model_path)

    def run_prediction(self, image_path: str, section: str, damage_type: str) -> Optional[List]:
        confidence = settings.DAMAGE_CONF_SCORES.get(damage_type, 0.10)
        output_path = os.path.join("/tmp", "detection", section)

        logger.info(f"Running prediction for {image_path}")
        logger.info(f"[{section}/{damage_type}] Running YOLO with confidence {confidence}...")

        results = self.model.predict(
            source=image_path,
            save=True,
            save_crop=True,
            conf=confidence,
            iou=0.5,
            line_width=2,
            project=output_path,
            name=damage_type,
            exist_ok=True
        )

        return results



