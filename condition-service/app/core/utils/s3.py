import os
import uuid

from app.core.utils import s3_client


def get_file(bucket: str, key: str) -> str:
    # Get file extension from original key
    _, ext = os.path.splitext(key)
    # Generate UUID-based filename with original extension
    uuid_filename = f"{uuid.uuid4()}{ext}"
    local_path = os.path.join("/tmp", uuid_filename)

    try:
        # Download file from S3
        with open(local_path, "wb") as f:
            s3_client.download_fileobj(bucket, key, f)

        return local_path

    except Exception as e:
        # Re-throw the exception to preserve original error information
        raise


def get_model(bucket: str, key: str) -> str:
    # Extract the original filename from the S3 key
    original_filename = os.path.basename(key)
    local_path = os.path.join("/tmp", original_filename)

    try:
        # Download file from S3
        with open(local_path, "wb") as f:
            s3_client.download_fileobj(bucket, key, f)

        return local_path

    except Exception as e:
        # Re-throw the exception to preserve original error information
        raise

